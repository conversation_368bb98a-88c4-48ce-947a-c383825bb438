package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroModeloContratoRedeEmpresaJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.ModeloContratoRedeEmpresaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaModeloContratoRedeEmpresa;
import com.pacto.cadastrosAuxiliares.swagger.respostas.modelocontrato.ExemploRespostaListModeloContratoRedeEmpresaPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/modelo-contrato-rede-empresa")
public class ModeloContratoRedeEmpresaController {

    private final ModeloContratoRedeEmpresaService modeloContratoRedeEmpresaService;

    public ModeloContratoRedeEmpresaController(ModeloContratoRedeEmpresaService modeloContratoRedeEmpresaService) {
        this.modeloContratoRedeEmpresaService = modeloContratoRedeEmpresaService;
    }

    @Operation(
            summary = "Consultar modelo de contrato rede empresa",
            description = "Consulta as informações de um modelo de contrato rede empresa pelo código identificador do modelo de contrato.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do modelo de contrato que será consultado", example = "15", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContratoRedeEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping("/contrato/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findByModeloContrato(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(modeloContratoRedeEmpresaService.findByModeloContratoRedeEmpresa(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar modelo de contrato rede empresa por código e chave",
            description = "Consulta as informações de um modelo de contrato rede empresa pelo código identificador do modelo de contrato e chave da unidade da rede.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do modelo de contrato que será consultado", example = "15", required = true),
                    @Parameter(name = "key", description = "Chave identificadora da unidade da rede de academias", example = "ACADEMIA_CENTRO", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContratoRedeEmpresa.class)
                            )
                    )
            }
    )
    @GetMapping("/modeloContrato/{id}/{key}")
    public ResponseEntity<EnvelopeRespostaDTO> findModeloContratoByCodAndKey(@PathVariable Integer id,
                                                                    @PathVariable String key) {
        try {
            return ResponseEntityFactory.ok(modeloContratoRedeEmpresaService.findModeloContratoRedeEmpresaByCodAndKey(id, key));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar empresas para replicar modelo de contrato",
            description = "Consulta as empresas da rede disponíveis para replicação de um modelo de contrato específico.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "key", description = "Chave identificadora da unidade origem da rede de academias", example = "ACADEMIA_MATRIZ", required = true),
                    @Parameter(name = "modeloContratoOrigem", description = "Código identificador do modelo de contrato origem que será replicado", example = "15", required = true),
                    @Parameter(name = "token", description = "Token de autenticação para acesso às informações da rede", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", required = true),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nomeUnidade,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome da unidade ou código do modelo de contrato",
                            example = "{\"quicksearchValue\":\"academia centro\"}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListModeloContratoRedeEmpresaPaginacao.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "/find-empresas-replicar-modelo-contrato/{key}/{modeloContratoOrigem}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresasReplicarModeloContrato(@PathVariable String key, @PathVariable Integer modeloContratoOrigem,
                                                                                  @RequestParam String token, @RequestParam (required = false) JSONObject filters,
                                                                                  @Parameter(hidden = true) @RequestParam (required = false) PaginadorDTO paginadorDTO) {
        try {

            return ResponseEntityFactory.ok(modeloContratoRedeEmpresaService.obterEmpresasReplicarModeloContrato(key,modeloContratoOrigem, new FiltroModeloContratoRedeEmpresaJSON(filters), token, paginadorDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Replicar modelo de contrato para unidade da rede",
            description = "Replica um modelo de contrato de academia de uma unidade origem para uma unidade destino da rede empresarial.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "modeloContratoOrigem", description = "Código identificador do modelo de contrato origem que será replicado", example = "15", required = true),
                    @Parameter(name = "chaveDestino", description = "Chave identificadora da unidade destino da rede de academias", example = "ACADEMIA_CENTRO", required = true),
                    @Parameter(name = "empresa", description = "Código identificador da empresa destino na rede", example = "123", required = true),
                    @Parameter(
                            name = "empresaId",
                            description = "Código identificador da empresa para a qual a operação será realizada",
                            required = true,
                            example = "1",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = Integer.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContratoRedeEmpresa.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "/replicar/{modeloContratoOrigem}/{chaveDestino}/{empresa}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarModeloContrato(@PathVariable Integer modeloContratoOrigem,
                                                                      @PathVariable String chaveDestino,
                                                                      @PathVariable Integer empresa){
        try {
            return ResponseEntityFactory.ok(modeloContratoRedeEmpresaService.replicar(modeloContratoOrigem, chaveDestino, empresa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Incluir modelo de contrato rede empresa",
            description = "Inclui um novo modelo de contrato na rede empresarial, associando uma unidade origem a uma unidade destino.",
            tags = {SwaggerTags.MODELO_CONTRATO},
            parameters = {
                    @Parameter(name = "modeloContratoOrigem", description = "Código identificador do modelo de contrato origem que será incluído", example = "15", required = true),
                    @Parameter(name = "chaveDestino", description = "Chave identificadora da unidade destino da rede de academias", example = "ACADEMIA_CENTRO", required = true),
                    @Parameter(name = "empresa", description = "Código identificador da empresa destino na rede", example = "123", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaModeloContratoRedeEmpresa.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "/{modeloContratoOrigem}/{chaveDestino}/{empresa}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirModeloContratroRedeEmpresa(@PathVariable Integer modeloContratoOrigem,
                                                                      @PathVariable String chaveDestino,
                                                                      @PathVariable Integer empresa){
        try {
            return ResponseEntityFactory.ok(modeloContratoRedeEmpresaService.incluir(modeloContratoOrigem, chaveDestino, empresa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
